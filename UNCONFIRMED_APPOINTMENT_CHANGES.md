# Unconfirmed Appointment Engagement Logic Update

## Overview
Updated the PMX+ engagement logic to ensure that unconfirmed appointments follow the Check-In → Reminder engagement stages instead of jumping straight to <PERSON><PERSON><PERSON> when BRG_CHIKIN correspondent (check-in) is enabled.

## Changes Made

### 1. Configuration Changes

#### `pmx-server/src/main/resources/application.yml`
- Added new configuration property: `op.pmx.unconfirmed-appointment.skip-checkin-hours-threshold`
- Default value: 24 hours
- Environment variable: `ENV_PMX_UNCONFIRMED_SKIP_CHECKIN_HOURS_THRESHOLD`

#### `pmx-server/src/main/resources/application-test.yml`
- Added the same configuration property for test environment

### 2. Code Changes

#### `pmx-server/src/main/kotlin/com/connexin/pmx/server/services/impl/EngagementStateMachineImpl.kt`

**Added Configuration Injection:**
- Added `@Value` annotation to inject the skip-checkin-hours-threshold configuration
- Added Spring's `@Value` import

**Added Helper Method:**
- `isCheckInEnabledForCustomer(context: EngagementContext): Boolean`
- Determines if check-in is enabled by checking if CHECKIN workflow rule exists

**Updated `handleCheckInitial` Method:**
- Added new condition to check for unconfirmed appointments with check-in enabled
- When check-in is enabled and appointment is unconfirmed, it goes to CHECK_IN stage
- Added logging to indicate when unconfirmed appointments go to check-in

**Updated `handleBookingSent` Method:**
- Added same logic as `handleCheckInitial` for consistency
- Ensures unconfirmed appointments after booking also go to check-in when enabled

**Updated `handleInitialStateToCheckIn` Method:**
- Modified to handle unconfirmed appointments properly
- Only records confirmation status if not unconfirmed

**Updated `handleStartCheckIn` Method:**
- Added logic to determine appropriate template scenario
- Uses `CONFIRMED_CHECK_IN` scenario for confirmed appointments
- Uses `DEFAULT` scenario for unconfirmed appointments
- Added appropriate logging messages

### 3. Test Changes

#### `pmx-server/src/test/kotlin/com/connexin/pmx/server/services/impl/EngagementStateMachineUnconfirmedAppointmentTest.kt`
- Created comprehensive test suite to verify the new functionality
- Tests both scenarios: with and without check-in enabled
- Verifies that unconfirmed appointments go to correct engagement stage

## Behavior Changes

### Before Changes
- Unconfirmed appointments would skip CHECK_IN stage and go directly to REMINDER
- This happened regardless of whether check-in was enabled for the customer

### After Changes
- **When check-in is enabled (BRG_CHIKIN correspondent):**
  - Unconfirmed appointments go to CHECK_IN stage first
  - Then proceed to REMINDER stage after check-in is complete
  - Follows the Check-In → Reminder engagement flow

- **When check-in is not enabled:**
  - Unconfirmed appointments still go directly to REMINDER stage
  - Maintains backward compatibility for customers without check-in

## Configuration

The new behavior is controlled by:
1. **Check-in availability**: Determined by presence of CHECKIN workflow rule
2. **Time threshold**: Configurable via `op.pmx.unconfirmed-appointment.skip-checkin-hours-threshold`

## Acceptance Criteria Verification

✅ **GIVEN** a patient has a scheduled appointment  
✅ **WHEN** a patient fails to CONFIRM the appointment  
✅ **THEN** do not skip the CHECKIN stage (when check-in is enabled)  
✅ **AND** the patient receives a Check-In link successfully  

## Testing

Run the new test suite:
```bash
./mvnw test -Dtest=EngagementStateMachineUnconfirmedAppointmentTest
```

## Environment Variables

Add to your environment configuration:
```
ENV_PMX_UNCONFIRMED_SKIP_CHECKIN_HOURS_THRESHOLD=24
```

This controls the time threshold for phase skipping behavior (currently used for future extensibility).
