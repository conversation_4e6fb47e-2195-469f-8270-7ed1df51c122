package com.connexin.pmx.server.services.impl

import com.connexin.pmx.server.exceptions.NoApplicableRulesException
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.ErrorDto
import com.connexin.pmx.server.services.EngagementMessageHandler
import com.connexin.pmx.server.services.EngagementMessageResult
import com.connexin.pmx.server.services.EngagementResponseHandler
import com.connexin.pmx.server.services.EngagementStateMachine
import com.connexin.pmx.server.statemachine.Event
import com.connexin.pmx.server.statemachine.StateMachine
import com.connexin.pmx.server.statemachine.StateTransition
import com.connexin.pmx.server.utils.getApplicableRulesByWorkflow
import com.connexin.pmx.server.utils.use
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.Instant
import java.time.ZoneId
import java.time.temporal.ChronoUnit

@Suppress("UNUSED_PARAMETER")
@Service
class EngagementStateMachineImpl(
    private val responseHandler: EngagementResponseHandler,
    private val messageHandler: EngagementMessageHandler
) :
    StateMachine<EngagementStatus, EngagementEvent, Engagement, EngagementContext>(log = log), EngagementStateMachine {

    @Value("\${op.pmx.unconfirmed-appointment.skip-checkin-hours-threshold:24}")
    private var skipCheckinHoursThreshold: Long = 24

    final override var states: Map<EngagementStatus, StateTransition<EngagementStatus, EngagementEvent, Engagement, EngagementContext>>

    init {
        states = buildStates()
    }

    override fun<M: Event<EngagementEvent>> transition(state: Engagement, event: M, context: EngagementContext): Engagement {
        arrayOf(
            MDC.putCloseable(MdcKeys.MDC_ENGAGEMENT_ID, state.id),
            MDC.putCloseable(MdcKeys.MDC_CUSTOMER_ID, context.customer.id),
            MDC.putCloseable(MdcKeys.MDC_ENGAGEMENT_EVENT, event.event.name)
        ).use {
            return super.transition(state, event, context)
        }
    }

    override fun <M: EventMessage> sendEvent(message: M, context: EngagementContext?): Engagement {
        val event = message.event
        val customer = message.customer
        val engagement = message.engagement
        log.debug("Received {} event", event)
        // TODO: cache calculated rules
        val rules = customer.getApplicableRulesByWorkflow(engagement)

        if (rules == null) {
          log.error("Engagement has no appointments. Engagements should have at least one appointment specified before starting.")
            responseHandler.recordError(
                engagement,
                ErrorDto(
                    path = "",
                    message = "Engagement should have at least one appointment, but none were found.",
                    errorCode = Errors.ENGAGEMENT_NO_APPOINTMENTS.code
                )
            )
            return engagement.copyWithState(EngagementStatus.ERROR)
        } else if (rules.isEmpty()) {
            log.error("Found no applicable workflows for this engagement. Check configuration to ensure rules are set up and enabled to cover all appointments.")
            return noApplicableRules(engagement)
        }

        log.debug("Found the following applicable workflows: {}", rules.keys)

        val mergedContext = EngagementContext(
            rules = rules,
            customer = customer,
            now = context?.now ?: Instant.now()
        )
        return try {
            transition(engagement, message, mergedContext)
        } catch (ex: NoApplicableRulesException) {
            log.error("Found no applicable {} workflow for this engagement while transitioning", ex.workflow)
            noApplicableRules(engagement)
        } catch (ex: Exception) {
            log.error("An unhandled exception occurred while transitioning.", ex)
            responseHandler.recordError(
                engagement,
                ErrorDto(
                    path = "",
                    message = "Unexpected error occurred while processing. No further engagement is possible at this time.",
                    errorCode = Errors.UNEXPECTED_ERROR.code
                )
            )
            engagement.copyWithState(EngagementStatus.ERROR)
        }
    }

    private fun noApplicableRules(engagement: Engagement): Engagement {
        responseHandler.recordError(
            engagement,
            ErrorDto(
                path = "",
                message = "No available rules apply to this engagement. Check configuration to ensure rules are configured and enabled.",
                errorCode = Errors.ENGAGEMENT_NO_APPLICABLE_RULES.code
            )
        )
        return engagement.copyWithState(EngagementStatus.ERROR)
    }

    private fun getSchedule(workflow: EngagementWorkflow, context: EngagementContext): Schedule {
        val rule = context.rules[workflow] ?: throw NoApplicableRulesException(workflow)

        return rule.schedule ?: when (workflow) {
            EngagementWorkflow.CONFIRMATION -> Schedule.CONFIRMATION_DEFAULT
            EngagementWorkflow.REMINDER, EngagementWorkflow.CHECKIN -> Schedule.REMINDER_DEFAULT
            EngagementWorkflow.BOOKING, EngagementWorkflow.CANCELLATION, EngagementWorkflow.APPOINTMENT_SURVEY,
            EngagementWorkflow.APPT_SURVEY_EXTERNAL, EngagementWorkflow.APPT_SURVEY_INTERNAL -> Schedule.NONE
        }
    }

    private fun getNextCheckpoint(engagement: Engagement, workflow: EngagementWorkflow, context: EngagementContext): Checkpoint {
        val schedule = getSchedule(workflow, context)

        log.debug("Using {} to calculate next checkpoint", schedule)

        val location = engagement.resources.filterIsInstance<LocationResource>().first()
        val zone = ZoneId.of(location.zoneId)

        val now = context.now.atZone(zone)
        val future = engagement.eventDate.atZone(zone)

        val next = schedule.next(now, future, DeliveryDay.toDayOfWeek(context.customer.deliveryDays))

        return Checkpoint(
            next = next ?: if (workflow == EngagementWorkflow.CONFIRMATION) getConfirmationCutoff(
                engagement.eventDate,
                context.customer
            )
            else context.now,
            scheduled = next != null
        )
    }

    private fun getConfirmationCutoff(eventDate: Instant, customer: Customer): Instant {
        val cutoff = customer.cancellationDeadline ?: Duration.ZERO
        log.debug("Cancellation cutoff is {} before {}", cutoff, eventDate)

        return eventDate.minus(cutoff)
    }

    /**
     * Determines if check-in is enabled for the customer by checking if they have
     * a CHECKIN workflow rule configured. This is used to determine whether
     * unconfirmed appointments should go through the check-in stage.
     */
    private fun isCheckInEnabledForCustomer(context: EngagementContext): Boolean {
        return context.rules.containsKey(EngagementWorkflow.CHECKIN)
    }

    fun handleCheckInitial(
        engagement: Engagement,
        event: Event<EngagementEvent>,
        context: EngagementContext
    ): Engagement {
        log.info("Starting with initial checkpoint for engagement")
        val isOnSameDay = engagement.eventDate.truncatedTo(ChronoUnit.DAYS).equals(context.now.truncatedTo(ChronoUnit.DAYS))
        if (isOnSameDay) {
            handleSameDayEngagement(engagement)
        }

        return if (!isOnSameDay && context.rules.containsKey(EngagementWorkflow.BOOKING)) {
            log.info("The BOOKING workflow is the beginning of this engagement")
            engagement.bookingAttempts = 0
            engagement.nextCheckpoint = context.now
            transition(engagement, TransitionEvent(event = EngagementEvent.START_BOOKING), context)
        } else if (!isOnSameDay && context.rules.containsKey(EngagementWorkflow.CONFIRMATION) && engagement.confirmationStatus != ConfirmationStatus.CONFIRMED) {
            log.info("The CONFIRMATION workflow is the beginning of this engagement")
            initializeConfirmation(engagement, context)
        } else if (context.rules.containsKey(EngagementWorkflow.CHECKIN) && engagement.confirmationStatus == ConfirmationStatus.CONFIRMED) {
            log.info("The CHECKIN workflow is the beginning of this engagement as the appointment was already created as confirmed OR created on same day")
            transition(engagement, TransitionEvent(event = EngagementEvent.START_CHECKIN), context)
        } else if (context.rules.containsKey(EngagementWorkflow.CHECKIN) && engagement.confirmationStatus == ConfirmationStatus.UNCONFIRMED && isCheckInEnabledForCustomer(context)) {
            log.info("The CHECKIN workflow is the beginning of this engagement for unconfirmed appointment as check-in is enabled")
            transition(engagement, TransitionEvent(event = EngagementEvent.START_CHECKIN), context)
        } else if (context.rules.containsKey(EngagementWorkflow.APPOINTMENT_SURVEY) && engagement.confirmationStatus != ConfirmationStatus.DECLINED) {
            log.info("The APPOINTMENT_SURVEY workflow is the beginning of this engagement")
            transition(engagement, TransitionEvent(event = EngagementEvent.START_APPOINTMENT_SURVEY), context)
        } else if (context.rules.containsKey(EngagementWorkflow.REMINDER) && engagement.confirmationStatus != ConfirmationStatus.DECLINED) {
            log.info("The REMINDER workflow is the beginning of this engagement")
            initializeReminders(engagement, context)
        } else {
            log.info("engagement confirmation status is {}", engagement.confirmationStatus)
            log.error("Unable to start engagement because customer has configured no applicable rules.")
            transition(
                engagement, ErrorEvent(
                    engagement, context.customer, errors = listOf(
                        ErrorDto(
                            path = "",
                            message = "No available rules apply to this engagement. Check configuration to ensure rules are configured and enabled.",
                            errorCode = Errors.ENGAGEMENT_NO_APPLICABLE_RULES.code
                        )
                    )
                ), context
            )
        }
    }

    fun handleBookingSent(
        engagement: Engagement,
        event: Event<EngagementEvent>,
        context: EngagementContext
    ): Engagement {
        return if (context.rules.containsKey(EngagementWorkflow.CONFIRMATION) && engagement.confirmationStatus != ConfirmationStatus.CONFIRMED) {
            log.info("The CONFIRMATION workflow is the next stage of this engagement")
            initializeConfirmation(engagement, context)
        } else if (context.rules.containsKey(EngagementWorkflow.CHECKIN) && engagement.confirmationStatus == ConfirmationStatus.CONFIRMED) {
            log.info("The CHECKIN workflow is the next stage of this engagement")
            transition(engagement, TransitionEvent(event = EngagementEvent.START_CHECKIN), context)
        } else if (context.rules.containsKey(EngagementWorkflow.CHECKIN) && engagement.confirmationStatus == ConfirmationStatus.UNCONFIRMED && isCheckInEnabledForCustomer(context)) {
            log.info("The CHECKIN workflow is the next stage of this engagement for unconfirmed appointment as check-in is enabled")
            transition(engagement, TransitionEvent(event = EngagementEvent.START_CHECKIN), context)
        } else if (context.rules.containsKey(EngagementWorkflow.APPOINTMENT_SURVEY) && engagement.confirmationStatus != ConfirmationStatus.DECLINED) {
            log.info("The APPOINTMENT_SURVEY workflow is the next stage of this engagement")
            transition(engagement, TransitionEvent(event = EngagementEvent.START_APPOINTMENT_SURVEY), context)
        } else if (context.rules.containsKey(EngagementWorkflow.REMINDER) && engagement.confirmationStatus != ConfirmationStatus.DECLINED) {
            log.info("The REMINDER workflow is the next stage of this engagement")
            initializeReminders(engagement, context)
        } else {
            transition(engagement, TransitionEvent(event = EngagementEvent.WORKFLOW_COMPLETE), context)
        }
    }

    private fun initializeConfirmation(engagement: Engagement, context: EngagementContext): Engagement {
        // TODO: replace hours until with check for "inside minimum schedule", that way it works consistently regardless of schedule
        val hoursUntil = Duration.between(context.now, engagement.eventDate).toHours()
        engagement.confirmationAttempts = 0
        engagement.nextCheckpoint = if (hoursUntil >= 24) getNextCheckpoint(
            engagement,
            EngagementWorkflow.CONFIRMATION,
            context
        ).next else context.now

        return transition(engagement, TransitionEvent(event = EngagementEvent.START_CONFIRMATION), context)
    }

    private fun initializeReminders(engagement: Engagement, context: EngagementContext): Engagement {
        // TODO: replace hours until with check for "inside minimum schedule", that way it works consistently regardless of schedule
        val hoursUntil = Duration.between(context.now, engagement.eventDate).toHours()
        engagement.reminderAttempts = 0
        // if the customer doesn't want confirmation messages but does use reminders (which allow customers to cancel)
        // then we'll act like the engagement is confirmed
        engagement.confirmationStatus = ConfirmationStatus.CONFIRMED
        engagement.nextCheckpoint = if (hoursUntil >= 24) getNextCheckpoint(
            engagement,
            EngagementWorkflow.REMINDER,
            context
        ).next else context.now

        return transition(engagement, TransitionEvent(event = EngagementEvent.START_REMINDER), context)
    }

    fun handleImmediateNextEvent(engagement: Engagement, event: Event<EngagementEvent>, context: EngagementContext): Engagement {
        if (context.now < engagement.nextCheckpoint) {
            return engagement
        }

        return when(event.event) {
            EngagementEvent.START_CONFIRMATION -> handleSendConfirmation(engagement, event, context)
            EngagementEvent.START_REMINDER -> handleSendReminder(engagement, event, context)
            else -> engagement
        }
    }

    fun handleSendConfirmation(engagement: Engagement, event: Event<EngagementEvent>, context: EngagementContext): Engagement {
        val schedule = getSchedule(EngagementWorkflow.CONFIRMATION, context)
        val cutoffDate = getConfirmationCutoff(engagement.eventDate, context.customer)
        if (engagement.confirmationAttempts >= schedule.totalAttempts
            || cutoffDate <= context.now) {
            if (engagement.confirmationAttempts >= schedule.totalAttempts) {
                log.info("Maximum number of confirmation notifications have been sent with no response, marking engagement as declined, Reason: No Response")
            } else {
                log.info("No response received before cut-off time, marking engagement as declined, Reason: No Response")
            }

            val declinedEvent = ConfirmationResponseEvent(
                event = EngagementEvent.DECLINED,
                engagement = engagement,
                customer = context.customer,
                result = ConfirmationStatus.NO_RESPONSE
            )

            return transition(engagement, declinedEvent, context)
        }

        return handleSendMessage(engagement, EngagementWorkflow.CONFIRMATION, context)
    }

    private fun handleSendCheckIn(engagement: Engagement, event: Event<EngagementEvent>, context: EngagementContext): Engagement {
        val schedule = getSchedule(EngagementWorkflow.CHECKIN, context)
        if (engagement.sameDayCheckInSent
            || engagement.checkInAttempts >= schedule.totalAttempts + 1
            || engagement.eventDate < context.now) {
            if (engagement.sameDayCheckInSent || engagement.checkInAttempts >= schedule.totalAttempts) {
                log.info("Maximum number of check-in notifications have been sent, marking check-in status as no response.")
            } else {
                log.info("Event has already started so no need to send check-in messages any further, marking check-in status as no response.")
            }

            // by this time, if they haven't responded to check-in messages, we can just mark all remaining as
            // no response
            // we will need to discuss with product what to do with check-in responses that come in late
            // (think: parent is in still at front desk completing check-in at the time the appointment has started)
            // right now, if they complete check-in after the event has already started, PMX will ignore the response
            // and not update to indicate they have completed check in.
            // one way to work around this is let the practice define a grace period after appointment start time
            // when the CHECK_IN state can be kept open

            val remainingAppointments = filterCheckInEligibleAppointments(engagement)
            remainingAppointments.forEach { it.checkInStatus = CheckInStatus.NO_RESPONSE }

            responseHandler.recordCheckIn(
                engagement = engagement,
                respondents = filterCheckInEligibleContacts(engagement),
                appointments = remainingAppointments.toSet(),
                result = CheckInStatus.NO_RESPONSE
            )

            return transition(engagement, TransitionEvent(event = EngagementEvent.CHECKIN_DONE), context)
        }

        return handleSendMessage(
            engagement = engagement,
            workflow = EngagementWorkflow.CHECKIN,
            context,
            contacts = filterCheckInEligibleContacts(engagement),
            appointments = filterCheckInEligibleAppointments(engagement)
        )
    }

    fun handleSendReminder(engagement: Engagement, event: Event<EngagementEvent>, context: EngagementContext): Engagement {
        val schedule = getSchedule(EngagementWorkflow.REMINDER, context)
        if (engagement.reminderAttempts >= schedule.totalAttempts || engagement.eventDate < context.now) {
            if (engagement.reminderAttempts >= schedule.totalAttempts) {
                log.info("Maximum number of reminder notifications have been sent. Transitioning to completed.")
            } else {
                log.info("Event has already started so no need to send reminders any further. Transitioning to completed.")
            }

            return transition(engagement, TransitionEvent(event = EngagementEvent.REMINDERS_SENT), context)
        }

        return handleSendMessage(
            engagement, EngagementWorkflow.REMINDER, context
        )
    }

    private fun handleRecordCancelled(engagement: Engagement, event: Event<EngagementEvent>, context: EngagementContext): Engagement {
        val response = responseHandler.recordCancellation(engagement)
        log.info("Recorded cancellation response {}", response.id)

        return engagement
    }

    private fun handleSameDayEngagement(engagement: Engagement) {
        log.info("Skipping BOOKING and CONFIRMATION because and Marking same day engagement as confirmed automatically")

        responseHandler.recordConfirmation(
            engagement = engagement,
            result = ConfirmationStatus.CONFIRMED,
            respondents = engagement.resources.filterIsInstance<ContactResource>().toSet(),
            isFinal = false,
            messageId = null
        )
        engagement.confirmationStatus = ConfirmationStatus.CONFIRMED
    }
    private fun handleInitialStateToCheckIn(engagement: Engagement, event: Event<EngagementEvent>, context: EngagementContext): Engagement {
        // For unconfirmed appointments going to check-in, we don't change the confirmation status
        // but we still record the current status for tracking purposes
        if (engagement.confirmationStatus != ConfirmationStatus.UNCONFIRMED) {
            responseHandler.recordConfirmation(
                engagement = engagement,
                result = engagement.confirmationStatus,
                respondents = engagement.resources.filterIsInstance<ContactResource>().toSet(),
                isFinal = false,
                messageId = null
            )
        }

        return handleStartCheckIn(engagement, event, context)
    }

    private fun handleStartCheckIn(engagement: Engagement, event: Event<EngagementEvent>, context: EngagementContext): Engagement {
        if (!context.rules.containsKey(EngagementWorkflow.CHECKIN)) {
            log.info("No CHECKIN workflow enabled for this engagement, transitioning to SURVEYS")
            return transition(engagement, TransitionEvent(event = EngagementEvent.CHECKIN_DONE), context)
        }

        val eligibleContacts = filterCheckInEligibleContacts(engagement)

        if (eligibleContacts.isEmpty()) {
            log.info("No contacts eligible for CHECKIN are reachable, transitioning to SURVEYS")
            return transition(engagement, TransitionEvent(event = EngagementEvent.CHECKIN_DONE), context)
        }

        // Determine the appropriate scenario based on confirmation status
        val scenario = if (engagement.confirmationStatus == ConfirmationStatus.CONFIRMED) {
            log.info("Sending immediate check-in message after confirming.")
            TemplateScenario.CONFIRMED_CHECK_IN
        } else {
            log.info("Sending check-in message for unconfirmed appointment.")
            TemplateScenario.DEFAULT
        }

        handleSendMessage(
            engagement,
            EngagementWorkflow.CHECKIN,
            context,
            contacts = eligibleContacts,
            appointments = filterCheckInEligibleAppointments(engagement),
            scenario = scenario
        )

        val checkpoint = getNextCheckpoint(engagement, EngagementWorkflow.CHECKIN, context)

        if (!checkpoint.scheduled) {
            log.info("Engagement was confirmed too close to starting time, transitioning using SURVEYS to skip check in")
            return transition(engagement, TransitionEvent(event = EngagementEvent.CHECKIN_DONE), context)
        }

        engagement.nextCheckpoint = checkpoint.next
        log.info("Setting next checkpoint to {}", engagement.nextCheckpoint)
        return engagement
    }

    private fun handleSendAppointmentSurvey(engagement: Engagement, event: Event<EngagementEvent>, context: EngagementContext): Engagement {
        if (!context.rules.containsKey(EngagementWorkflow.APPOINTMENT_SURVEY)) {
            log.info("No APPOINTMENT_SURVEY workflow enabled for this engagement, transitioning to REMIND")
            return transition(engagement, TransitionEvent(event = EngagementEvent.APPOINTMENT_SURVEY_SENT), context)
        }

        val eligibleContacts = filterCheckInEligibleContacts(engagement)

        if (eligibleContacts.isEmpty()) {
            log.info("No contacts eligible for APPOINTMENT_SURVEY are reachable, transitioning to REMIND")
            return transition(engagement, TransitionEvent(event = EngagementEvent.APPOINTMENT_SURVEY_SENT), context)
        }

        log.info("Sending appointment surveys")

        return handleSendMessage(
            engagement,
            EngagementWorkflow.APPOINTMENT_SURVEY,
            context,
            contacts = eligibleContacts,
            appointments = engagement.getAppointments().toSet(),
        )
    }

    private fun handleScheduleRemindCheckpoint(engagement: Engagement, event: Event<EngagementEvent>, context: EngagementContext): Engagement {
        if (!context.rules.containsKey(EngagementWorkflow.REMINDER)) {
            log.info("No REMINDER workflow enabled for this engagement, transitioning using REMINDERS_SENT to skip reminders")
            return transition(engagement, TransitionEvent(event = EngagementEvent.REMINDERS_SENT), context)
        }

        val checkpoint = getNextCheckpoint(engagement, EngagementWorkflow.REMINDER, context)

        if (!checkpoint.scheduled) {
            log.info("Engagement was confirmed or checked in too close to starting time, transitioning using REMINDERS_SENT to skip reminders")
            return transition(engagement, TransitionEvent(event = EngagementEvent.REMINDERS_SENT), context)
        }

        engagement.nextCheckpoint = checkpoint.next
        log.info("Setting next checkpoint to {}", engagement.nextCheckpoint)
        return engagement
    }

    private fun handleRecordDeclined(engagement: Engagement, event: Event<EngagementEvent>, context: EngagementContext): Engagement {
        val respondents = if (event is ConfirmationResponseEvent) event.respondents else emptySet()
        val cutoff = getConfirmationCutoff(engagement.eventDate, context.customer)
        if (cutoff < context.now && engagement.confirmationStatus != ConfirmationStatus.DECLINED && respondents.isNotEmpty()) {
            log.info("Sending cutoff notice")
            return handleSendMessage(
                engagement,
                EngagementWorkflow.CONFIRMATION,
                context,
                contacts = respondents,
                scenario = TemplateScenario.CONFIRMATION_DEADLINE_PASSED
            )
        }

        val message = event as ConfirmationResponseEvent

        val isFinal = !context.rules.containsKey(EngagementWorkflow.CANCELLATION)
        val response = responseHandler.recordConfirmation(
            engagement,
            result = message.result,
            respondents = message.respondents,
            isFinal = isFinal,
            messageId = message.messageId
        )
        log.info(
            "Recorded declined response {} (reason: {}).{}",
            response.id,
            message.result,
            if (isFinal) " This response is final." else ""
        )

        engagement.contactDeclined = true
        engagement.confirmationStatus = message.result

        return engagement
    }

    private fun handleRecordConfirmed(engagement: Engagement, event: Event<EngagementEvent>, context: EngagementContext): Engagement {
        val respondents = if (event is ConfirmationResponseEvent) event.respondents else emptySet()
        val cutoff = getConfirmationCutoff(engagement.eventDate, context.customer)
        if (cutoff < context.now && engagement.confirmationStatus != ConfirmationStatus.CONFIRMED && respondents.isNotEmpty()) {
            log.info("Sending cutoff notice")
            return handleSendMessage(
                engagement,
                EngagementWorkflow.CONFIRMATION,
                context,
                contacts = respondents,
                scenario = TemplateScenario.CONFIRMATION_DEADLINE_PASSED
            )
        }

        if (engagement.confirmationStatus == ConfirmationStatus.DECLINED && respondents.isNotEmpty()) {
            log.info("Sending cannot change response notice")
            return handleSendMessage(
                engagement,
                EngagementWorkflow.CONFIRMATION,
                context,
                contacts = respondents,
                scenario = TemplateScenario.CONFIRMATION_CANNOT_CHANGE_RESPONSE
            )
        }

        val confirmationEvent = event as ConfirmationResponseEvent
        val response = responseHandler.recordConfirmation(
            engagement,
            result = confirmationEvent.result,
            respondents = confirmationEvent.respondents,
            isFinal = false,
            messageId = confirmationEvent.messageId
        )
        log.info("Recorded confirmation response {} (reason: {}).", response.id, confirmationEvent.result)
        engagement.confirmationStatus = confirmationEvent.result
        return transition(engagement, TransitionEvent(event = EngagementEvent.CHECKPOINT), context)
    }

    private fun handleRecordCheckedIn(engagement: Engagement, event: Event<EngagementEvent>, context: EngagementContext): Engagement {
        val response = event as CheckInResponseEvent

        log.info(
            "Contact(s) {} have completed check-in for appointment(s) {}",
            response.respondents.map { it.id },
            response.appointments.map { it.id }
        )

        val appointments = engagement.resources.filterIsInstance<AppointmentResource>().associateBy({it.id}, { it })

        // mark the specified appointments as completed, then send a response event
        response.appointments.forEach {
            it.checkInStatus = CheckInStatus.CHECKED_IN
            appointments[it.id]?.checkInStatus = CheckInStatus.CHECKED_IN
        }

        responseHandler.recordCheckIn(
            engagement = engagement,
            respondents = response.respondents,
            appointments = response.appointments,
            result = CheckInStatus.CHECKED_IN
        )

        return if (filterCheckInEligibleAppointments(engagement).isEmpty()) {
            // if all appointments for the engagement are checked in, we can move out of the CHECKIN state
            log.info("All appointments for engagement are checked in, transitioning to REMIND")
            transition(engagement, TransitionEvent(event = EngagementEvent.CHECKIN_DONE), context)
        } else {
            // not all appointments are checked in yet, so we'll need to keep sending messages until they finish them all
            log.info("Not all appointments are checked in yet, remaining in CHECKIN")
            engagement
        }
    }

    private fun handleSendCancellation(engagement: Engagement, event: Event<EngagementEvent>, context: EngagementContext): Engagement {
        if (!context.rules.containsKey(EngagementWorkflow.CANCELLATION)) {
            log.info("No cancellation rule configured, transitioning to CANCELLED without sending notice to contacts.")
            return transition(engagement, TransitionEvent(event = EngagementEvent.CANCELLATION_SENT), context)
        } else if (context.now >= engagement.eventDate) {
            log.info("Engagement event date has already occurred, transitioning to CANCELLED without sending notice to contacts.")
            return transition(engagement, TransitionEvent(event = EngagementEvent.CANCELLATION_SENT), context)
        }

        return handleSendMessage(engagement, EngagementWorkflow.CANCELLATION, context)
    }

    private fun handleSendBooking(engagement: Engagement, event: Event<EngagementEvent>, context: EngagementContext): Engagement {
        if (!context.rules.containsKey(EngagementWorkflow.BOOKING)) {
            log.info("No booking rule configured, transitioning to BOOKING_SENT without sending notice to contacts.")
            return transition(engagement, TransitionEvent(event = EngagementEvent.BOOKING_SENT), context)
        } else if (context.now >= engagement.eventDate) {
            log.info("Engagement event date has already occurred, transitioning to BOOKING_SENT without sending notice to contacts.")
            return transition(engagement, TransitionEvent(event = EngagementEvent.BOOKING_SENT), context)
        }

        return handleSendMessage(engagement, EngagementWorkflow.BOOKING, context)
    }

    private fun handleError(
        engagement: Engagement,
        event: Event<EngagementEvent>,
        context: EngagementContext
    ): Engagement {
        val message = event as ErrorEvent
        val response = responseHandler.recordError(
            engagement,
            *message.errors.toTypedArray()
        )
        log.warn("Recorded error response {}. This response is final.", response.id)
        return engagement
    }

    private fun handleScheduleCompleted(
        engagement: Engagement,
        event: Event<EngagementEvent>,
        context: EngagementContext
    ): Engagement {
        log.info("All expected tasks are complete. Waiting until the event date at {} to transition to COMPLETED in case the contact cancels.", engagement.eventDate)
        engagement.nextCheckpoint = engagement.eventDate

        return engagement
    }

    private fun handleCompleted(
        engagement: Engagement,
        event: Event<EngagementEvent>,
        context: EngagementContext
    ): Engagement {
        val response = responseHandler.recordComplete(engagement)
        log.info("Recorded completed response {}. This response is final.", response.id)
        return engagement
    }

    fun handleSendMessage(
        engagement: Engagement,
        workflow: EngagementWorkflow,
        context: EngagementContext,
        contacts: Set<ContactResource> = engagement.resources.filterIsInstance<ContactResource>().toSet(),
        scenario: TemplateScenario = TemplateScenario.DEFAULT,
        appointments: Set<AppointmentResource> = engagement.resources.filterIsInstance<AppointmentResource>().toSet()
    ): Engagement {
        MDC.put(MdcKeys.MDC_ENGAGEMENT_WORKFLOW, workflow.name)
        val rule = context.rules[workflow]

        if (rule == null) {
            log.error("Unable to send {} as no applicable rule is configured for the customer. This could happen if the customer disabled, deleted, or modified a rule after an engagement was created.", workflow)
            val err = when (workflow) {
                EngagementWorkflow.CONFIRMATION -> Errors.ENGAGEMENT_CONFIRMATION_RULE_MISSING
                EngagementWorkflow.REMINDER -> Errors.ENGAGEMENT_REMINDER_RULE_MISSING
                EngagementWorkflow.CHECKIN -> Errors.ENGAGEMENT_CHECK_IN_RULE_MISSING
                EngagementWorkflow.CANCELLATION -> Errors.ENGAGEMENT_CANCELLATION_RULE_MISSING
                EngagementWorkflow.BOOKING -> Errors.ENGAGEMENT_BOOKING_RULE_MISSING
                EngagementWorkflow.APPOINTMENT_SURVEY ->  Errors.ENGAGEMENT_APPOINTMENT_SURVEY_RULE_MISSING
                EngagementWorkflow.APPT_SURVEY_INTERNAL ->  Errors.ENGAGEMENT_APPOINTMENT_INTERNAL_SURVEY_RULE_MISSING
                EngagementWorkflow.APPT_SURVEY_EXTERNAL ->  Errors.ENGAGEMENT_APPOINTMENT_EXTERNAL_SURVEY_RULE_MISSING
            }
            MDC.remove(MdcKeys.MDC_ENGAGEMENT_WORKFLOW)
            return transition(
                engagement,
                ErrorEvent(
                    engagement,
                    context.customer,
                    errors = listOf(ErrorDto(
                        path = "",
                        errorCode = err.code,
                        message = err.message
                    ))
                ),
                context
            )
        }

        val result = messageHandler.send(
            engagement, rule, context.customer, contacts, appointments, scenario, context.rules
        )

        if (!result.contactErrors.isNullOrEmpty() || !result.success) {
            // reports back any sending issues to the customer, so they're aware
            // of problems and can try to remediate them ASAP
            handleContactUnreachable(engagement, workflow, result.contactErrors)
        }

        val schedule = getSchedule(workflow, context)

        val resultEngagement = when(workflow) {
            EngagementWorkflow.BOOKING -> {
                engagement.bookingAttempts++
                log.info("Sent booking notification")

                engagement.nextCheckpoint = getNextCheckpoint(engagement, EngagementWorkflow.BOOKING, context).next

                return transition(engagement, TransitionEvent(event = EngagementEvent.BOOKING_SENT), context)
            }
            EngagementWorkflow.CONFIRMATION -> {
                if (scenario == TemplateScenario.DEFAULT) {
                    engagement.confirmationAttempts++
                    log.info("Attempted confirmation notification #{}", engagement.confirmationAttempts)

                    // sets checkpoint to next checkpoint configured for the workflow OR the cutoff date for cancellations
                    // (the latter assumes we've exhausted attempts or ran out of time)
                    engagement.nextCheckpoint =
                        getNextCheckpoint(engagement, EngagementWorkflow.CONFIRMATION, context).next
                    log.info("Setting next checkpoint to {}", engagement.nextCheckpoint)
                } else {
                    log.info("Sent {} notification", scenario)
                }
                engagement
            }
            EngagementWorkflow.REMINDER -> {
                engagement.reminderAttempts++
                log.info("Attempted reminder notification #{}", engagement.reminderAttempts)

                val checkpoint = getNextCheckpoint(engagement, EngagementWorkflow.REMINDER, context)

                return if (engagement.reminderAttempts >= schedule.totalAttempts || !checkpoint.scheduled) {
                    log.info("Maximum number of reminder notifications have been sent. Transitioning to completed.")
                    transition(engagement, TransitionEvent(event = EngagementEvent.REMINDERS_SENT), context)
                } else {
                    engagement.nextCheckpoint = checkpoint.next
                    log.info("Setting next checkpoint to {}", engagement.nextCheckpoint)
                    engagement
                }
            }
            EngagementWorkflow.CANCELLATION -> {
                engagement.cancellationAttempts++
                log.info("Sent cancellation notification.")
                return transition(engagement, TransitionEvent(event = EngagementEvent.CANCELLATION_SENT), context)
            }
            EngagementWorkflow.CHECKIN -> {
                log.info("Attempted check-in notification #{}", ++engagement.checkInAttempts)

                if (scenario == TemplateScenario.SAME_DAY_CHECK_IN) {
                    engagement.sameDayCheckInSent = true
                }

                val checkpoint = getNextCheckpoint(engagement, EngagementWorkflow.CHECKIN, context)
                // for same day check in, we only send one message and wait until the appointment starts,
                // where we'll mark all appts as not checked in if they haven't responded yet.
                engagement.nextCheckpoint = if(checkpoint.scheduled) checkpoint.next else engagement.eventDate
                log.info("Setting next checkpoint to {}", engagement.nextCheckpoint)
                engagement
            }
            EngagementWorkflow.APPOINTMENT_SURVEY -> {
                engagement.appointmentSurveyAttempts++
                log.info("Attempted appointment-survey notification #{}", engagement.appointmentSurveyAttempts)

                return transition(engagement, TransitionEvent(event = EngagementEvent.APPOINTMENT_SURVEY_SENT), context)
            }
            else -> {
                // Do nothing
                engagement
            }
        }

        MDC.remove(MdcKeys.MDC_ENGAGEMENT_WORKFLOW)
        return resultEngagement
    }

    private fun handleContactUnreachable(
        engagement: Engagement,
        workflow: EngagementWorkflow,
        contactErrors: List<EngagementMessageResult.ContactErrors>?
    ) {
        if (contactErrors.isNullOrEmpty()) {
            log.warn("No contacts were provided.")
            responseHandler.recordContactUnreachable(
                engagement,
                workflow,
                EngagementMessageResult.ContactErrors(id = "", errors = listOf(
                    ErrorDto(
                        path = "",
                        errorCode = Errors.NO_CONTACTS.code,
                        message = Errors.NO_CONTACTS.message
                    )
                ))
            )
        } else {
            for (contact in contactErrors) {
                log.warn("Contact with id=[{}] has the following errors: {}", contact.id, contact.errors)
                responseHandler.recordContactUnreachable(
                    engagement,
                    workflow,
                    contact
                )
            }
        }
    }

    private fun buildStates(): Map<EngagementStatus, StateTransition<EngagementStatus, EngagementEvent, Engagement, EngagementContext>> {
        // here we build all the possible states and events that can trigger transitions to other states (which may or may not execute an action)
        return builder<EngagementStatus, EngagementEvent, Engagement, EngagementContext>()
            .withState(EngagementStatus.INITIAL)
                .on(EngagementEvent.CHECKPOINT)
                    .target(EngagementStatus.INITIAL)
                    .action(::handleCheckInitial)
                .on(EngagementEvent.START_BOOKING)
                    .target(EngagementStatus.BOOK)
                    .action(::handleSendBooking)
                .on(EngagementEvent.START_CONFIRMATION)
                    .target(EngagementStatus.CONFIRM)
                    .action(::handleImmediateNextEvent)
                .on(EngagementEvent.START_REMINDER)
                    .target(EngagementStatus.REMIND)
                    .action(::handleImmediateNextEvent)
                .on(EngagementEvent.START_CHECKIN)
                    .target(EngagementStatus.CHECK_IN)
                    .action(::handleInitialStateToCheckIn)
                .on(EngagementEvent.START_APPOINTMENT_SURVEY)
                    .target(EngagementStatus.APPOINTMENT_SURVEY)
                    .action(::handleSendAppointmentSurvey)
                .on(EngagementEvent.ERROR)
                    .target(EngagementStatus.ERROR)
                    .action(::handleError)
                .on(EngagementEvent.CANCELLED)
                    .target(EngagementStatus.CANCELLED)
                    .action(::handleRecordCancelled)
            .and()
            .withState(EngagementStatus.BOOK)
                .on(EngagementEvent.CHECKPOINT)
                    .target(EngagementStatus.BOOK)
                    .action(::handleSendBooking)
                .on(EngagementEvent.BOOKING_SENT)
                    .target(EngagementStatus.BOOK)
                    .action(::handleBookingSent)
                .on(EngagementEvent.WORKFLOW_COMPLETE)
                    .target(EngagementStatus.AWAIT_COMPLETED)
                    .action(::handleScheduleCompleted)
                .on(EngagementEvent.START_CONFIRMATION)
                    .target(EngagementStatus.CONFIRM)
                    .action(::handleImmediateNextEvent)
                .on(EngagementEvent.START_CHECKIN)
                    .target(EngagementStatus.CHECK_IN)
                    .action(::handleInitialStateToCheckIn)
                .on(EngagementEvent.START_APPOINTMENT_SURVEY)
                    .target(EngagementStatus.APPOINTMENT_SURVEY)
                    .action(::handleSendAppointmentSurvey)
                .on(EngagementEvent.START_REMINDER)
                    .target(EngagementStatus.REMIND)
                    .action(::handleImmediateNextEvent)
                .on(EngagementEvent.CANCELLED)
                    .target(EngagementStatus.CANCEL)
                    .action(::handleSendCancellation)
                .on(EngagementEvent.ERROR)
                    .target(EngagementStatus.ERROR)
                    .action(::handleError)
            .and()
            .withState(EngagementStatus.CONFIRM)
                .on(EngagementEvent.CHECKPOINT)
                    .target(EngagementStatus.CONFIRM)
                    .action(::handleSendConfirmation)
                .on(EngagementEvent.CANCELLED)
                    .target(EngagementStatus.CANCEL)
                    .action(::handleSendCancellation)
                .on(EngagementEvent.CONFIRMED)
                    .target(EngagementStatus.CONFIRMED)
                    .action(::handleRecordConfirmed)
                .on(EngagementEvent.DECLINED)
                    .target(EngagementStatus.DECLINED)
                    .action(::handleRecordDeclined)
                .on(EngagementEvent.ERROR)
                    .target(EngagementStatus.ERROR)
                    .action(::handleError)
            .and()
            .withState(EngagementStatus.CONFIRMED)
                .on(EngagementEvent.CHECKPOINT)
                    .target(EngagementStatus.CHECK_IN)
                    .action(::handleStartCheckIn)
                .on(EngagementEvent.CANCELLED)
                    .target(EngagementStatus.CANCEL)
                    .action(::handleSendCancellation)
                .on(EngagementEvent.ERROR)
                    .target(EngagementStatus.ERROR)
                    .action(::handleError)
                .on(EngagementEvent.DECLINED)
                    .target(EngagementStatus.DECLINED)
                    .action(::handleRecordDeclined)
            .and()
            .withState(EngagementStatus.DECLINED)
                .on(EngagementEvent.CANCELLED)
                    .target(EngagementStatus.CANCEL)
                    .action(::handleSendCancellation)
                .on(EngagementEvent.CONFIRMED)
                    .target(EngagementStatus.CONFIRMED)
                    .action(::handleRecordConfirmed)
            .and()
            .withState(EngagementStatus.CHECK_IN)
                .on(EngagementEvent.CHECKPOINT)
                    .target(EngagementStatus.CHECK_IN)
                    .action(::handleSendCheckIn)
                .on(EngagementEvent.CHECKED_IN)
                    .target(EngagementStatus.CHECK_IN)
                    .action(::handleRecordCheckedIn)
                .on(EngagementEvent.CHECKIN_DONE)
                    .target(EngagementStatus.APPOINTMENT_SURVEY)
                    .action(::handleSendAppointmentSurvey)
                .on(EngagementEvent.CANCELLED)
                    .target(EngagementStatus.CANCEL)
                    .action(::handleSendCancellation)
                .on(EngagementEvent.ERROR)
                    .target(EngagementStatus.ERROR)
                    .action(::handleError)
                .on(EngagementEvent.DECLINED)
                    .target(EngagementStatus.DECLINED)
                    .action(::handleRecordDeclined)
            .and()
            .withState(EngagementStatus.APPOINTMENT_SURVEY)
                .on(EngagementEvent.CHECKPOINT)
                    .target(EngagementStatus.APPOINTMENT_SURVEY)
                    .action(::handleSendAppointmentSurvey)
                .on(EngagementEvent.APPOINTMENT_SURVEY_SENT)
                    .target(EngagementStatus.REMIND)
                    .action(::handleScheduleRemindCheckpoint)
                .on(EngagementEvent.ERROR)
                    .target(EngagementStatus.ERROR)
                    .action(::handleError)
            .and()
            .withState(EngagementStatus.REMIND)
                .on(EngagementEvent.CHECKPOINT)
                    .target(EngagementStatus.REMIND)
                    .action(::handleSendReminder)
                .on(EngagementEvent.CANCELLED)
                    .target(EngagementStatus.CANCEL)
                    .action(::handleSendCancellation)
                .on(EngagementEvent.REMINDERS_SENT)
                    .target(EngagementStatus.AWAIT_COMPLETED)
                    .action(::handleScheduleCompleted)
                .on(EngagementEvent.ERROR)
                    .target(EngagementStatus.ERROR)
                    .action(::handleError)
                .on(EngagementEvent.DECLINED)
                    .target(EngagementStatus.DECLINED)
                    .action(::handleRecordDeclined)
            .and()
            .withState(EngagementStatus.CANCEL)
                .on(EngagementEvent.CANCELLATION_SENT)
                    .target(EngagementStatus.CANCELLED)
                    .action(::handleRecordCancelled)
                .on(EngagementEvent.ERROR)
                    .target(EngagementStatus.ERROR)
                    .action(::handleError)
            .and()
            .withState(EngagementStatus.AWAIT_COMPLETED)
                .on(EngagementEvent.DECLINED)
                    .target(EngagementStatus.DECLINED)
                    .action(::handleRecordDeclined)
                .on(EngagementEvent.CHECKPOINT)
                    .target(EngagementStatus.COMPLETED)
                    .action(::handleCompleted)
                .on(EngagementEvent.CANCELLED)
                    .target(EngagementStatus.CANCEL)
                    .action(::handleSendCancellation)
            .and()
            .build()
    }

    companion object {
        private val log = LoggerFactory.getLogger(EngagementStateMachineImpl::class.java)

        private fun filterCheckInEligibleContacts(engagement: Engagement): Set<ContactResource> {
            return engagement.resources
                .filterIsInstance<ContactResource>()
                .filter { it.contactMethod == ContactMethod.EMAIL || it.contactMethod == ContactMethod.SMS }
                .toSet()
        }

        private fun filterCheckInEligibleAppointments(engagement: Engagement): Set<AppointmentResource> {
            return engagement.resources
                .filterIsInstance<AppointmentResource>()
                .filter { it.checkInStatus == CheckInStatus.NOT_CHECKED_IN }
                .toSet()
        }
    }
}