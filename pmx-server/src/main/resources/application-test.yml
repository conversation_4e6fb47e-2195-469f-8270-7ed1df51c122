op:
  atlas:
    practice:
      url: https://atlas-dev.op.healthcare/api/v1/atlas-practice/
    survey:
      url: https://atlas-dev.op.healthcare/api/v1/atlas-survey/
    token-url: https://officepracticum-test.auth0.com/oauth/token
    client-id: jR2HFiGRhJsWU7hwkHaLBheMnukcJu63
    client-secret: ****************************************************************
    audience: https://api.officepracticum.com/atlas
  pmx:
    scheduling:
      enabled: false
    base-url: http://localhost:8080
    confirmation-base-url: http://localhost:8080/pmx
    provisioning-disable-vendor-calls: true
    admin-password: test
    engagement-scheduler-page-size: 100
    engagement-scheduler-parallel-batch-size: 10
    engagement-checkpoint-delay-time-seconds: 300
    unconfirmed-appointment:
      skip-checkin-hours-threshold: 24
    jobs:
      delivery-stats:
        cron: "-"
  url-shortener:
    api-key: test
  bridge:
    url: https://applications-dev.op.healthcare/bridge
spring:
  data:
    mongodb:
      uri: ${ENV_MONGO_URI:*********************************************************}
      auto-index-creation: false
  cache:
    type: none

redis:
  enabled: false
telnyx:
    api-key: test
    public-key: dGVzdHRlc3R0ZXN0dGVzdHRlc3R0ZXN0dGVzdHRlc3Q=
    engagement:
      english-messaging-profile-id: test-english-messaging
      spanish-messaging-profile-id: test-spanish-messaging
      english-voice-numbers: +14405551234
      spanish-voice-numbers: +14405554321
      call-control-connection-id: test-call-control
keycloak:
  token_url: http://localhost:9988/auth/realms/test-realm/protocol/openid-connect/token
  client_id: test-atlas-pmx-client-id
  client_secret: test-atlas-pmx-client-secret
  bridge_scope: read:bridge write:bridge

