package com.connexin.pmx.server.services.impl

import com.connexin.atlas.sl.survey.dto.RemoteSurveyLinkDto
import com.connexin.atlas.sl.survey.dto.SurveyType
import com.connexin.pmx.server.models.*
import com.connexin.pmx.server.models.dtos.CreateMessageRequest
import com.connexin.pmx.server.models.dtos.CreateMessageResponse
import com.connexin.pmx.server.services.CheckInService
import com.connexin.pmx.server.services.PmxMessageService
import com.connexin.pmx.server.services.SurveyService
import com.connexin.pmx.server.services.TemplateService
import com.connexin.pmx.server.services.impl.TemplateTestData.appointmentDate
import com.connexin.pmx.server.services.impl.TemplateTestData.crappyTemplate
import com.connexin.pmx.server.services.impl.TemplateTestData.customer
import com.connexin.pmx.server.services.impl.TemplateTestData.engagement
import com.connexin.pmx.server.services.impl.TemplateTestData.engagementRulesMap
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.junit5.MockKExtension
import io.mockk.verifySequence
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.Instant

@ExtendWith(MockKExtension::class)
class EngagementMessageHandlerImplTests {
    @RelaxedMockK
    lateinit var messageService: PmxMessageService

    @RelaxedMockK
    lateinit var templateService: TemplateService

    @RelaxedMockK
    lateinit var checkInService: CheckInService

    @RelaxedMockK
    lateinit var surveyService: SurveyService

    @InjectMockKs
    lateinit var sut: EngagementMessageHandlerImpl

    @BeforeEach
    fun setup() {
        every { templateService.buildSubstitutions(any(), any(), any()) } returns mapOf(
            Language.ENGLISH to mutableMapOf(),
            Language.SPANISH to mutableMapOf()
        )
        every { templateService.generateMessageSegments(any(), any(), any()) } returns MessageSegments(
            main = "",
            altMain = "",
            subject = "",
            instructions = ""
        )
        every { templateService.generateMessageSegments(match { it.templateIds.containsValue(crappyTemplate.id!!) }, any(), any() ) } returns null
    }

    @Test
    fun `send should fail if all attempts to send fail`() {
        every { messageService.create(any()) } answers {
            val request = firstArg<CreateMessageRequest>()
            CreateMessageResponse(
                success = false,
                message = PmxMessage(
                    id = "test",
                    customerId = request.customerId,
                    type = request.type,
                    status = MessageStatus.QUEUED,
                    sendAfter = Instant.now(),
                    message = request.message,
                    subject = request.subject
                )
            )
        }

        val actual = sut.send(engagement, customer.engagementRules.elementAt(0), customer, rules = engagementRulesMap)

        assertThat(actual.success).isFalse
        assertThat(actual.contactErrors).hasSize(6)

        verifySequence {
            messageService.create(any())
            messageService.create(any())
            messageService.create(any())
            messageService.create(any())
            messageService.create(any())
            messageService.create(any())
        }
    }

    @Test
    fun `send should fail if creating PMX messages throw`() {
        every { messageService.create(any()) } throws Exception("test")

        val actual = sut.send(engagement, customer.engagementRules.elementAt(0), customer, rules = engagementRulesMap)

        assertThat(actual.success).isFalse
        assertThat(actual.contactErrors).hasSize(6)
        assertThat(actual.contactErrors).allMatch { contactErrors -> contactErrors.errors.any { it.errorCode == Errors.UNEXPECTED_ERROR.code } }
    }

    @Test
    fun `send should fail if contacts missing phone number or email`() {
        val badContacts = Engagement(
            id = "test",
            customerId = customer.id!!,
            eventDate = appointmentDate,
            nextCheckpoint = appointmentDate,
            resources = mutableSetOf(
                PracticeResource(
                    id = "practice1",
                    name = "Practice One"
                ),
                LocationResource(
                    id = "location1",
                    name = "Location One",
                    address = "123 Main St",
                    practiceId = "practice1",
                    phone = "************",
                    zipCode = "44145",
                    zoneId = "US/Eastern"
                ),
                StaffResource(
                    id = "staff1",
                    name = "Dr Staff 1"
                ),
                PatientResource(
                    id = "patient1",
                    givenName = "Patient 1",
                    familyName = "Test"
                ),
                ContactResource(
                    id = "contact1",
                    givenName = "English Email Contact",
                    familyName = "Test",
                    contactMethod = ContactMethod.EMAIL,
                    language = Language.ENGLISH,
                    email = null,
                    phone = null
                ),
                ContactResource(
                    id = "contact2",
                    givenName = "Spanish SMS Contact",
                    familyName = "Test",
                    contactMethod = ContactMethod.SMS,
                    language = Language.ENGLISH,
                    email = null,
                    phone = null
                ),
                ContactResource(
                    id = "contact3",
                    givenName = "English Voice Contact",
                    familyName = "Test",
                    contactMethod = ContactMethod.VOICE,
                    language = Language.ENGLISH,
                    email = null,
                    phone = null
                ),
                AppointmentResource(
                    id = "appointment1",
                    startTime = appointmentDate,
                    reason = "Test 1",
                    location = "location1",
                    staff = "staff1",
                    patient = "patient1",
                    practice = "practice1",
                    appointmentType = "apptType1"
                ),
            )
        )

        val actual = sut.send(badContacts, customer.engagementRules.elementAt(0), customer, rules = engagementRulesMap)

        assertThat(actual.success).isFalse
        assertThat(actual.contactErrors).hasSize(3)
        assertThat(actual.contactErrors).allMatch { contactError -> contactError.errors.any { it.errorCode == Errors.INVALID_EMAIL_OR_PHONE.code } }
    }

    @Test
    fun `send should fail if contact provided invalid contact method`() {
        val badContacts = Engagement(
            id = "test",
            customerId = customer.id!!,
            eventDate = appointmentDate,
            nextCheckpoint = appointmentDate,
            resources = mutableSetOf(
                PracticeResource(
                    id = "practice1",
                    name = "Practice One"
                ),
                LocationResource(
                    id = "location1",
                    name = "Location One",
                    address = "123 Main St",
                    practiceId = "practice1",
                    phone = "************",
                    zipCode = "44145",
                    zoneId = "US/Eastern"
                ),
                StaffResource(
                    id = "staff1",
                    name = "Dr Staff 1"
                ),
                PatientResource(
                    id = "patient1",
                    givenName = "Patient 1",
                    familyName = "Test"
                ),
                ContactResource(
                    id = "contact1",
                    givenName = "English Email Contact",
                    familyName = "Test",
                    contactMethod = ContactMethod.NONE,
                    language = Language.ENGLISH,
                    email = null,
                    phone = null
                ),
                AppointmentResource(
                    id = "appointment1",
                    startTime = appointmentDate,
                    reason = "Test 1",
                    location = "location1",
                    staff = "staff1",
                    patient = "patient1",
                    practice = "practice1",
                    appointmentType = "apptType1"
                ),
            )
        )

        val actual = sut.send(badContacts, customer.engagementRules.elementAt(0), customer, rules = engagementRulesMap)

        assertThat(actual.success).isFalse
        assertThat(actual.contactErrors).hasSize(1)
        assertThat(actual.contactErrors).allMatch { contactError -> contactError.errors.any { it.errorCode == Errors.INVALID_CONTACT_METHOD.code } }
    }

    @Test
    fun `send should fail if appropriate message for contact and language preference is missing`() {
        val rule = EngagementRule(
            id = "1",
            workflow = EngagementWorkflow.CONFIRMATION,
            templateIds = mapOf(TemplateScenario.DEFAULT to crappyTemplate.id!!)
        )

        val actual = sut.send(engagement, rule, customer, rules = engagementRulesMap)

        assertThat(actual.success).isFalse
        assertThat(actual.contactErrors).allMatch { contactError -> contactError.errors.any { it.errorCode == Errors.CONTACT_METHOD_OR_LANGUAGE_TEMPLATE_MISSING.code } }
    }

    @Test
    fun `send should succeed if at least one attempt succeeds`() {
        every { messageService.create(any()) } answers {
            val request = firstArg<CreateMessageRequest>()
            CreateMessageResponse(
                success = request.to.contains("+14405550004"),
                message = if (request.to.contains("+14405550004")) PmxMessage(
                    id = "test",
                    customerId = request.customerId,
                    type = request.type,
                    status = MessageStatus.QUEUED,
                    sendAfter = Instant.now(),
                    message = request.message,
                    subject = request.subject
                ) else null
            )
        }

        val actual = sut.send(engagement, customer.engagementRules.elementAt(0), customer, rules = engagementRulesMap)

        assertThat(actual.success).isTrue

        verifySequence {
            messageService.create(any())
            messageService.create(any())
            messageService.create(any())
            messageService.create(any())
            messageService.create(any())
            messageService.create(any())
        }
    }

    @Test
    fun `send should skip voice contacts if the template is cancellation but the user declined`() {
        val rule = EngagementRule(
            id = "1",
            workflow = EngagementWorkflow.CANCELLATION,
            templateIds = mapOf(TemplateScenario.DEFAULT to "good")
        )

        val declinedEngagement = Engagement(
            id = "test",
            customerId = customer.id!!,
            eventDate = appointmentDate,
            nextCheckpoint = appointmentDate,
            contactDeclined = true,
            resources = mutableSetOf(
                PracticeResource(
                    id = "practice1",
                    name = "Practice One"
                ),
                LocationResource(
                    id = "location1",
                    name = "Location One",
                    address = "123 Main St",
                    practiceId = "practice1",
                    phone = "************",
                    zipCode = "44145",
                    zoneId = "US/Eastern"
                ),
                StaffResource(
                    id = "staff1",
                    name = "Dr Staff 1"
                ),
                PatientResource(
                    id = "patient1",
                    givenName = "Patient 1",
                    familyName = "Test"
                ),
                ContactResource(
                    id = "contact1",
                    givenName = "English Email Contact",
                    familyName = "Test",
                    contactMethod = ContactMethod.VOICE,
                    language = Language.ENGLISH,
                    email = null,
                    phone = "+14405551234"
                ),
                AppointmentResource(
                    id = "appointment1",
                    startTime = appointmentDate,
                    reason = "Test 1",
                    location = "location1",
                    staff = "staff1",
                    patient = "patient1",
                    practice = "practice1",
                    appointmentType = "apptType1"
                ),
            )
        )

        val actual = sut.send(declinedEngagement, rule, customer, rules = engagementRulesMap)

        assertThat(actual.success).isTrue
    }

    @Test
    fun `send should not skip voice contacts if the template is cancellation and not user declined`() {
        every { messageService.create(any()) } answers {
            val request = firstArg<CreateMessageRequest>()
            CreateMessageResponse(
                success = true,
                message = PmxMessage(
                    id = "test",
                    customerId = request.customerId,
                    type = request.type,
                    status = MessageStatus.QUEUED,
                    sendAfter = Instant.now(),
                    message = request.message,
                    subject = request.subject
                )
            )
        }

        val rule = EngagementRule(
            id = "1",
            workflow = EngagementWorkflow.CANCELLATION,
            templateIds = mapOf(TemplateScenario.DEFAULT to "good")
        )

        val declinedEngagement = Engagement(
            id = "test",
            customerId = customer.id!!,
            eventDate = appointmentDate,
            nextCheckpoint = appointmentDate,
            contactDeclined = false,
            resources = mutableSetOf(
                PracticeResource(
                    id = "practice1",
                    name = "Practice One"
                ),
                LocationResource(
                    id = "location1",
                    name = "Location One",
                    address = "123 Main St",
                    practiceId = "practice1",
                    phone = "************",
                    zipCode = "44145",
                    zoneId = "US/Eastern"
                ),
                StaffResource(
                    id = "staff1",
                    name = "Dr Staff 1"
                ),
                PatientResource(
                    id = "patient1",
                    givenName = "Patient 1",
                    familyName = "Test"
                ),
                ContactResource(
                    id = "contact1",
                    givenName = "English Email Contact",
                    familyName = "Test",
                    contactMethod = ContactMethod.VOICE,
                    language = Language.ENGLISH,
                    email = null,
                    phone = "+14405551234"
                ),
                AppointmentResource(
                    id = "appointment1",
                    startTime = appointmentDate,
                    reason = "Test 1",
                    location = "location1",
                    staff = "staff1",
                    patient = "patient1",
                    practice = "practice1",
                    appointmentType = "apptType1"
                ),
            )
        )

        val actual = sut.send(declinedEngagement, rule, customer, rules = engagementRulesMap)

        assertThat(actual.success).isTrue
    }

    @Test
    fun `send should send individual messages for each appointment with check in links `() {
        every { messageService.create(any()) } answers {
            val request = firstArg<CreateMessageRequest>()
            CreateMessageResponse(
                success = request.to.contains("+14405550004"),
                message = if (request.to.contains("+14405550004")) PmxMessage(
                    id = "test",
                    customerId = request.customerId,
                    type = request.type,
                    status = MessageStatus.QUEUED,
                    sendAfter = Instant.now(),
                    message = request.message,
                    subject = request.subject
                ) else null
            )
        }

        val appointment1 = AppointmentResource(
            id = "1",
            startTime = Instant.now(),
            reason = "reason",
            location = "location",
            staff = "staff",
            patient = "patient1",
            appointmentType = "at",
            practice = "practice"
        )
        val appointment2 = AppointmentResource(
            id = "2",
            startTime = Instant.now(),
            reason = "reason",
            location = "location",
            staff = "staff",
            patient = "patient1",
            appointmentType = "at",
            practice = "practice"
        )
        val contact1 = ContactResource(
            id = "1",
            familyName = "fake",
            givenName = "contact",
            email = null,
            phone = "************",
            contactMethod = ContactMethod.SMS,
            language = Language.ENGLISH
        )

        every { checkInService.generateCheckInLink(appointment1, contact1, customer.id!!) } returns "link1"
        every { checkInService.generateCheckInLink(appointment2, contact1, customer.id!!) } returns "link2"

        val actual = sut.send(engagement, customer.engagementRules.elementAt(3), customer, appointments = setOf(appointment1, appointment2), rules = engagementRulesMap)

        assertThat(actual.success).isTrue

        // 12 messages will be sent, 1 for each appointment/contact combo (there are 6 contacts and 2 appointments)
        verifySequence {
            messageService.create(any())
            messageService.create(any())
            messageService.create(any())
            messageService.create(any())
            messageService.create(any())
            messageService.create(any())
            messageService.create(any())
            messageService.create(any())
            messageService.create(any())
            messageService.create(any())
            messageService.create(any())
            messageService.create(any())
        }
    }

    @Test
    fun `send should send survey message for confirmed appointment`() {
        var contactId = "6"
        var contactPhone = "+19085052407"
        var responseMessageId = "7"
        every { messageService.create(any()) } answers {
            val request = firstArg<CreateMessageRequest>()
            CreateMessageResponse(
                success = request.to.contains(contactPhone),
                message = if (request.to.contains(contactPhone)) PmxMessage(
                    id = responseMessageId,
                    customerId = request.customerId,
                    type = request.type,
                    status = MessageStatus.QUEUED,
                    sendAfter = Instant.now(),
                    message = request.message,
                    subject = request.subject
                ) else null
            )
        }

        val surveyRule = EngagementRule(
            id = "1",
            workflow = EngagementWorkflow.APPOINTMENT_SURVEY,
            templateIds = mutableMapOf()
        )

        val confirmedEngagement = Engagement(
            id = "2",
            customerId = customer.id!!,
            eventDate = appointmentDate,
            nextCheckpoint = appointmentDate,
            contactDeclined = false,
            confirmationStatus = ConfirmationStatus.CONFIRMED,
            resources = mutableSetOf(
                PracticeResource(
                    id = "3",
                    name = "Practice One"
                ),
                LocationResource(
                    id = "4",
                    name = "location",
                    address = "address",
                    practiceId = "3",
                    phone = "+14445559999",
                    zipCode = "44145",
                    zoneId = "US/Eastern"
                ),
                AppointmentResource(
                    id = "5",
                    startTime = Instant.now(),
                    reason = "reason",
                    location = "location",
                    staff = "staff",
                    patient = "patient",
                    appointmentType = "type",
                    practice = "practice"
                ),
                ContactResource(
                    id = contactId,
                    familyName = "family",
                    givenName = "given",
                    email = null,
                    phone = contactPhone,
                    contactMethod = ContactMethod.SMS,
                    language = Language.ENGLISH
                )
            )
        )

        val remoteSurvey = RemoteSurveyLinkDto()
        remoteSurvey.surveyQueueId = 1
        remoteSurvey.url = "shortenedUrl"
        remoteSurvey.surveyType = SurveyType.INTERNAL
        remoteSurvey.pinCode = "123"


        every { surveyService.generateSurveyLinks(customer, "patient") } returns listOf(remoteSurvey)

        val actual = sut.send(confirmedEngagement, surveyRule, customer, rules = engagementRulesMap, appointments = confirmedEngagement.getAppointments().toSet())

        assertThat(actual.success).isTrue

//        verifySequence {
//            messageService.create(any())
//        }
    }
}